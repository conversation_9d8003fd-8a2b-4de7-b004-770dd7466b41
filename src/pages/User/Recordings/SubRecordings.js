import React, { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import moment from "moment";
import { Checkbox, Tooltip } from "antd";
// import { BsDownload } from "react-icons/bs";
import { ReactComponent as TranscriptionIcon } from "./Assets/ic_translate_chats_blue.svg";
import { ReactComponent as RecordIcon } from "./Assets/Vector.svg";
import { ReactComponent as AttendanceIcon } from "./Assets/attendance.svg";
import { ReactComponent as ShareIcon } from "./Assets/share.svg";
import { ReactComponent as DownloadIcon } from "./Assets/download.svg";
import "./recording.scss";
import "../Transcript/transcription.scss";
import { encoder, modalNotification, baseUrlGenerator } from "../../../utils";
import { ANALYSIS_STATUS } from "../../../utils/constants";
import { VideoConferenceService } from "../../../services/User/VideoConference/index.service";
import Thumbnail from "./Assets/thumbnail.png";
import userRoutesMap from "../../../routeControl/userRoutes";
// import {
//   selectProfileData,
//   selectSubscriptionData,
// } from "../../../redux/UserSlice/index.slice";

const TRANSCRIPTION_STATUS_DISPLAY = {
  [ANALYSIS_STATUS.COMPLETED]: 'Completed',
  [ANALYSIS_STATUS.TRANSCRIPTION_FAILED]: 'Failed',
  [ANALYSIS_STATUS.ANALYSIS_FAILED]: 'Failed',
  [ANALYSIS_STATUS.QUEUED]: 'Queued',
  [ANALYSIS_STATUS.TRANSCRIPTION_INPROGRESS]: 'In Process',
  [ANALYSIS_STATUS.ANALYSIS_INPROGRESS]: 'In Process',
  [ANALYSIS_STATUS.UNAVAILABLE]: 'Not Available',
  null: 'Not Available'
};

const getTranscriptionStatus = (status) => {
  return TRANSCRIPTION_STATUS_DISPLAY[status] || 'Not Available';
};

const formatTimeInHoursMinutesSeconds = (seconds) => {
  if (!seconds) return "00:00:00";
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

/**
 * @typedef {Object} SubRecordingsProps
 * @property {Array} filteredRecordings - List of filtered recordings
 * @property {Function} setSelectedRecording - Function to set selected recording
 * @property {Function} setVideoUrl - Function to set video URL
 * @property {Function} formatDate - Function to format date
 * @property {Function} setShowDelete - Function to set delete state
 * @property {Function} setShowDownload - Function to set download state
 */

export default function SubRecordings({
  filteredRecordings,
  setSelectedRecording,
  setRecordingAndAnalysisPageUrl,
  formatDate,
  setShowDelete,
  setShowDownload,
}) {
  const [selectedRecordings, setSelectedRecordings] = useState([]);
  const [loadingAttendance, setLoadingAttendance] = useState({});
  // const userProfile = useSelector(selectProfileData);
  // const userSubscription = useSelector(selectSubscriptionData);
  // const userName = userProfile?.username;
  const baseUrl = baseUrlGenerator();
  // const isMeetingTranscriptionEnabled = userSubscription[1]?.Subscription?.SubscriptionFeature?.meeting_analysis;

  // Reset selections when filtered recordings change
  useEffect(() => {
    setSelectedRecordings([]);
  }, [filteredRecordings]);

  useEffect(() => {
    if (selectedRecordings.length > 0) {
      setShowDelete(true);
      setShowDownload(true);
      setSelectedRecording(
        filteredRecordings.filter((item) => selectedRecordings.includes(item.id))
      );
    } else {
      setShowDelete(false);
      setShowDownload(false);
      setSelectedRecording([]);
    }
  }, [selectedRecordings, filteredRecordings, setSelectedRecording, setShowDelete, setShowDownload]);

  const handleDownloadTranscription = (id) => {
    const transcription = filteredRecordings.find((item) => item.id === id);
    const transcriptData = transcription?.transcription_data;

    if (!transcriptData) {
      modalNotification({
        type: "error",
        message: "No transcription data available",
      });
      return;
    }

    let textFileData = "";

    // Add file header with metadata
    textFileData += "╔════════════════════════════════════════════════════════════╗\n";
    textFileData += "║                     TRANSCRIPTION FILE                     ║\n";
    textFileData += "╚════════════════════════════════════════════════════════════╝\n\n";

    // Add metadata section
    textFileData += "📋 METADATA\n";
    textFileData += "────────────────────────────────────────────────────────────\n";
    textFileData += `Generated: ${new Date().toLocaleString()}\n`;
    textFileData += `Duration: ${formatTimeInHoursMinutesSeconds(transcription?.duration_minutes || 0)}\n\n`;

    // Add Key Takeaways section
    if (transcriptData?.takeaways?.length > 0) {
      textFileData += "💡 KEY INSIGHTS / TAKEAWAYS\n";
      textFileData += "────────────────────────────────────────────────────────────\n\n";
      transcriptData.takeaways.forEach((item, index) => {
        textFileData += `${index + 1}. [${formatTimeInHoursMinutesSeconds(item?.start_time)}] ${item?.text}\n`;
      });
      textFileData += "\n";
    }

    // Add Summary section
    if (transcriptData?.summary) {
      textFileData += "📝 SUMMARY\n";
      textFileData += "────────────────────────────────────────────────────────────\n\n";
      textFileData += `${transcriptData.summary}\n\n`;
    }

    // Add Action Items section
    if (transcriptData?.action_items?.length > 0) {
      textFileData += "✅ ACTION ITEMS\n";
      textFileData += "────────────────────────────────────────────────────────────\n\n";
      transcriptData.action_items.forEach((item, index) => {
        textFileData += `${index + 1}. [${formatTimeInHoursMinutesSeconds(item?.start_time)}] ${item?.text}\n`;
      });
      textFileData += "\n";
    }

    // Add Transcription section
    if (transcriptData?.transcription?.transcription) {
      textFileData += "🎙️ TRANSCRIPTION\n";
      textFileData += "────────────────────────────────────────────────────────────\n\n";
      transcriptData.transcription.transcription.forEach((item) => {
        textFileData += `[${formatTimeInHoursMinutesSeconds(item?.start_time)}] ${item?.transcript}\n`;
      });
    }

    // Add footer
    textFileData += "\n\n";
    textFileData += "────────────────────────────────────────────────────────────\n";
    textFileData += "End of Transcription\n";

    const blob = new Blob([textFileData], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `transcription_${id}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleDownloadAttendance = async (recording) => {
    const meetingId = recording?.Meeting?.id;

    if (!meetingId) {
      modalNotification({
        type: "error",
        message: "Missing meeting information",
      });
      return;
    }

    setLoadingAttendance(prev => ({ ...prev, [recording.id]: true }));

    try {
      const attendanceResponse = await VideoConferenceService.getAttendanceReportService(meetingId);

      if (attendanceResponse.success) {
        const data = attendanceResponse.data;

        // Create CSV content
        let csvContent = "data:text/csv;charset=utf-8,";
        csvContent += "Name,Join Time,Leave Time,Duration,Email,Role\n";

        // Process session data - getAttendanceReportService returns data with MeetingSessionLogs
        if (data?.MeetingSessionLogs && data.MeetingSessionLogs.length > 0) {
          data.MeetingSessionLogs.forEach((session) => {
            // Add participant data for this session
            if (session.MeetingAttendances && session.MeetingAttendances.length > 0) {
              session.MeetingAttendances.forEach((participant) => {
                const joinTime = participant.joined_at ? moment(participant.joined_at).format("hh:mm A") : "N/A";
                const leaveTime = participant.leave_at ? moment(participant.leave_at).format("hh:mm A") : "Still in meeting";
                const duration = participant.duration_formatted || "N/A";
                const email = participant.email || "N/A";
                const role = participant.role || "participant";

                csvContent += `"${participant.screen_name || 'Unknown'}","${joinTime}","${leaveTime}","${duration}","${email}","${role}"\n`;
              });
            }
          });
        } else {
          csvContent += "No participants data available\n";
        }

        // Download the CSV file
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", `attendance_${recording.title}_${moment().format("YYYY-MM-DD")}.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        modalNotification({
          type: "success",
          message: "Attendance report downloaded successfully",
        });
      } else {
        modalNotification({
          type: "error",
          message: attendanceResponse.message || "Failed to fetch attendance data",
        });
      }
    } catch (error) {
      console.error('Error downloading attendance:', error);
      modalNotification({
        type: "error",
        message: "Error downloading attendance report",
      });
    } finally {
      setLoadingAttendance(prev => ({ ...prev, [recording.id]: false }));
    }
  };

  const handleShare = (recording) => {
    setSelectedRecording(recording);
    const shareUrl = `${baseUrl}${userRoutesMap.SHOW_TRANSCRIPT.path}/${encoder(recording?.Meeting?.id)}?recordingId=${encoder(recording?.id)}&sessionId=${encoder(recording?.MeetingSessionLog?.id)}`;
    setRecordingAndAnalysisPageUrl(shareUrl);
    window.navigator.clipboard.writeText(shareUrl);
    modalNotification({
      type: "success",
      message: "Link copied to clipboard",
    });
  };

  return (
    <main className="recordings-table">
      <table>
        <thead>
          <tr>
            <th>
              <label>
                <Checkbox
                  aria-label="Select all rows"
                  className="transcription-checkbox"
                  checked={
                    filteredRecordings.length > 0 &&
                    selectedRecordings.length === filteredRecordings.length
                  }
                  indeterminate={
                    selectedRecordings.length > 0 &&
                    selectedRecordings.length < filteredRecordings.length
                  }
                  onChange={(e) => {
                    setSelectedRecordings(
                      e.target.checked
                        ? filteredRecordings.map((item) => item.id)
                        : []
                    );
                  }}
                />
              </label>
            </th>
            <th>Recording Clips</th>
            <th>Session Start Time</th>
            <th>Meeting Date</th>
            <th>Recording Size / Duration</th>
            <th>Transcription Status</th>
            <th>Action Items</th>
          </tr>
        </thead>
        <tbody>
          {filteredRecordings.map((items, index) => (
            <tr key={index} className="recordings-row">
              <td>
                <div>
                  <Checkbox
                    checked={selectedRecordings.includes(items?.id)}
                    className="transcription-checkbox"
                    onChange={() => {
                      if (selectedRecordings.includes(items?.id)) {
                        setSelectedRecordings(
                          selectedRecordings.filter((id) => id !== items?.id)
                        );
                      } else {
                        setSelectedRecordings([...selectedRecordings, items?.id]);
                      }
                    }}
                  />
                </div>
              </td>
              <td className="recording-clip">
                <Link
                  className="recording-clip-container"
                  to={`${userRoutesMap.SHOW_TRANSCRIPT.path}/${encoder(
                    items?.Meeting?.id
                  )}?recordingId=${encoder(items?.id)}&sessionId=${encoder(items?.MeetingSessionLog?.id)}`}
                >
                  <RecordIcon className="record-icon" />
                  <img src={Thumbnail} alt="Thumbnail" />
                  <div className="recording-details">
                    <h3>{items?.title}</h3>
                  </div>
                </Link>
              </td>
              <td>{moment(items?.MeetingSessionLog?.started_at).format("hh:mm A")}</td>
              <td>{formatDate(items?.Meeting?.start_date)}</td>
              <td>
                {items?.recording_size_formatted || "N/A"}
                &nbsp;/&nbsp;
                {items.duration_minutes} {items.duration_minutes > 1 ? "mins" : "min"}
              </td>
              <td className={`${items?.transcription_data?.status}`}>
                {/* {isMeetingTranscriptionEnabled === 0 && items?.transacrption_status === null ? (
                  <span>Plan Not Available</span>
                ) : (

                )} */}
                <span>{getTranscriptionStatus(items?.transcription_data?.status)}</span>
              </td>
              <td className="action">
                <Tooltip title="Download Recording">
                  <DownloadIcon
                    className="action-icon"
                    onClick={() => window.open(items?.recording_url, "_blank")}
                  />
                </Tooltip>
                {items?.transcription_data?.status === ANALYSIS_STATUS.COMPLETED && (
                  <Tooltip title="Download Transcription">
                    <TranscriptionIcon
                      onClick={() => handleDownloadTranscription(items?.id)}
                    />
                  </Tooltip>
                )}
                <Tooltip title="Download Attendance">
                  <AttendanceIcon
                    className={`action-icon ${loadingAttendance[items.id] ? 'loading' : ''}`}
                    onClick={() => !loadingAttendance[items.id] && handleDownloadAttendance(items)}
                    style={{
                      opacity: loadingAttendance[items.id] ? 0.5 : 1,
                      cursor: loadingAttendance[items.id] ? 'not-allowed' : 'pointer'
                    }}
                  />
                </Tooltip>
                <Tooltip title="Share Video">
                  <ShareIcon
                    className="action-icon"
                    onClick={() => handleShare(items)}
                  />
                </Tooltip>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </main>
  );
}
